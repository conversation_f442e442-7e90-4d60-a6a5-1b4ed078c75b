<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trendyol Yorum Çekici</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .search-section {
            padding: 40px;
            background: #f8f9fa;
        }

        .search-form {
            display: flex;
            gap: 15px;
            max-width: 600px;
            margin: 0 auto;
        }

        .search-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 50px;
            font-size: 1.1rem;
            outline: none;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: #ff6b6b;
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
        }

        .search-btn {
            padding: 15px 30px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
        }

        .search-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #ff6b6b;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results {
            display: none;
            padding: 40px;
        }

        .product-info {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            border-left: 5px solid #ff6b6b;
        }

        .product-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .product-price {
            font-size: 1.2rem;
            color: #e74c3c;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .product-url {
            color: #3498db;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .comments-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .comments-title {
            font-size: 1.8rem;
            color: #2c3e50;
            font-weight: 600;
        }

        .comments-count {
            background: #ff6b6b;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
        }

        .comment-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .comment-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .comment-author {
            font-weight: 600;
            color: #2c3e50;
        }

        .comment-rating {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #f39c12;
            font-weight: 600;
        }

        .comment-text {
            color: #555;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .comment-date {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .error {
            display: none;
            background: #e74c3c;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 40px;
            text-align: center;
        }

        .no-comments {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .comment-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-comments"></i> Trendyol Yorum Çekici</h1>
            <p>Trendyol'dan ürün yorumlarını kolayca çekin ve analiz edin</p>
        </div>

        <div class="search-section">
            <form class="search-form" id="searchForm">
                <input 
                    type="text" 
                    class="search-input" 
                    id="searchInput" 
                    placeholder="Ürün adını girin (örn: iPhone 14, Samsung TV...)"
                    required
                >
                <button type="submit" class="search-btn" id="searchBtn">
                    <i class="fas fa-search"></i>
                    Yorumları Çek
                </button>
            </form>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Yorumlar çekiliyor, lütfen bekleyin...</p>
        </div>

        <div class="error" id="error"></div>

        <div class="results" id="results">
            <div class="product-info" id="productInfo"></div>
            
            <div class="comments-header">
                <h2 class="comments-title">Kullanıcı Yorumları</h2>
                <span class="comments-count" id="commentsCount">0 Yorum</span>
            </div>
            
            <div id="commentsList"></div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="test.js"></script>
</body>
</html>
æ