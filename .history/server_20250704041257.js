const express = require("express");
const cors = require("cors");
const path = require("path");
const axios = require("axios");
const cheerio = require("cheerio");

const app = express();
const PORT = 3001;

// OpenAI API anahtarınızı buraya ekleyin (opsiyonel)
const OPENAI_API_KEY =
    process.env.OPENAI_API_KEY ||
    "********************************************************************************************************************************************************************";

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static("public"));

// Ana sayfa
app.get("/", (req, res) => {
    res.sendFile(path.join(__dirname, "public", "index.html"));
});

// AI ile ürün URL'i bulma fonksiyonu
async function findProductUrlWithAI(searchQuery) {
    if (OPENAI_API_KEY) {
        try {
            const response = await fetch("https://api.openai.com/v1/chat/completions", {
                method: "POST",
                headers: {
                    Authorization: `Bearer ${OPENAI_API_KEY}`,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    model: "gpt-3.5-turbo",
                    messages: [
                        {
                            role: "user",
                            content: `"${searchQuery}" için Trendyol'da arama yapacağım. Bu ürün için en uygun arama terimini ve kategori bilgisini ver. Sadece arama terimi döndür, başka açıklama yapma.`,
                        },
                    ],
                    max_tokens: 100,
                    temperature: 0.3,
                }),
            });

            const data = await response.json();

            console.log(data);

            return null;
            // return data.choices[0].message.content.trim();
        } catch (error) {
            console.log("AI API hatası:", error.message);
            return searchQuery;
        }
    }
    return searchQuery;
}

// Trendyol'dan gerçek veri çekme fonksiyonu
async function scrapeRealTrendyolData(searchQuery) {
    try {
        // AI ile optimize edilmiş arama terimi al
        const optimizedQuery = await findProductUrlWithAI(searchQuery);
        console.log(`Optimize edilmiş arama terimi: ${optimizedQuery}`);

        // Trendyol arama sayfasını çek
        const searchUrl = `https://www.trendyol.com/sr?q=${encodeURIComponent(optimizedQuery)}`;
        console.log(`Arama URL'i: ${searchUrl}`);

        const searchResponse = await axios.get(searchUrl, {
            headers: {
                "User-Agent":
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "tr-TR,tr;q=0.8,en-US;q=0.5,en;q=0.3",
                "Accept-Encoding": "gzip, deflate, br",
                Connection: "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            },
            timeout: 10000,
        });

        const $ = cheerio.load(searchResponse.data);

        // İlk ürünün linkini bul
        let productLink = null;
        $('a[href*="/p/"]').each((i, element) => {
            if (i === 0) {
                productLink = $(element).attr("href");
                return false;
            }
        });

        if (!productLink) {
            throw new Error("Ürün bulunamadı");
        }

        const fullProductUrl = productLink.startsWith("http")
            ? productLink
            : `https://www.trendyol.com${productLink}`;
        console.log(`Ürün URL'i: ${fullProductUrl}`);

        // Ürün sayfasını çek
        const productResponse = await axios.get(fullProductUrl, {
            headers: {
                "User-Agent":
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "tr-TR,tr;q=0.8,en-US;q=0.5,en;q=0.3",
            },
            timeout: 15000,
        });

        const productPage = cheerio.load(productResponse.data);

        // Ürün bilgilerini çek
        const productTitle = productPage("h1").first().text().trim() || "Ürün adı bulunamadı";
        const productPrice =
            productPage(".prc-dsc, .prc-slg, .price-current").first().text().trim() ||
            "Fiyat bulunamadı";

        // Yorumları çek
        const comments = [];
        productPage('[data-testid="review-card"], .review-item, .comment-item').each(
            (i, element) => {
                if (i < 10) {
                    // İlk 10 yorum
                    const commentText = productPage(element)
                        .find('[data-testid="review-content"], .review-text, .comment-text')
                        .text()
                        .trim();
                    const rating = productPage(element)
                        .find('[data-testid="review-rating"], .rating, .star-rating')
                        .text()
                        .trim();
                    const author =
                        productPage(element).find(".review-author, .user-name").text().trim() ||
                        "Anonim Kullanıcı";
                    const date =
                        productPage(element).find(".review-date, .comment-date").text().trim() ||
                        "Tarih belirtilmemiş";

                    if (commentText) {
                        comments.push({
                            comment: commentText,
                            rating: rating || "5/5",
                            author: author,
                            date: date,
                        });
                    }
                }
            }
        );

        return {
            success: true,
            productTitle,
            productPrice,
            productUrl: fullProductUrl,
            comments,
            dataSource: "Real Trendyol Data",
        };
    } catch (error) {
        console.error("Scraping hatası:", error.message);
        return null;
    }
}

// Sahte veri oluşturma fonksiyonu
function generateMockComments(productName) {
    const positiveComments = [
        "Harika bir ürün, çok memnun kaldım!",
        "Kalitesi gerçekten çok iyi, tavsiye ederim.",
        "Hızlı kargo, ürün açıklamaya uygun geldi.",
        "Fiyat performans açısından mükemmel.",
        "Beklentilerimi karşıladı, teşekkürler.",
        "Çok beğendim, tekrar alırım.",
        "Kaliteli malzeme, işçilik güzel.",
        "Paketleme özenli, ürün sağlam geldi.",
    ];

    const neutralComments = [
        "İdare eder, fiyatına göre normal.",
        "Beklediğim gibi, ortalama bir ürün.",
        "Gayet kullanılabilir, sorun yok.",
        "Fena değil ama çok da süper değil.",
        "Standart kalite, şikayetim yok.",
    ];

    const negativeComments = [
        "Beklediğim gibi değildi, hayal kırıklığı.",
        "Kalitesi düşük, pişman oldum.",
        "Resimde gözüktüğü gibi değil.",
        "Pahalı, bu fiyata daha iyisi var.",
        "Kargo çok geç geldi, memnun değilim.",
    ];

    const userNames = [
        "Ahmet K.",
        "Ayşe Y.",
        "Mehmet D.",
        "Fatma S.",
        "Ali R.",
        "Zeynep T.",
        "Mustafa A.",
        "Elif B.",
        "Emre C.",
        "Selin M.",
        "Burak Ö.",
        "Deniz L.",
        "Cem P.",
        "Gizem N.",
        "Onur V.",
    ];

    const comments = [];
    const commentCount = Math.floor(Math.random() * 5) + 6; // 6-10 arası yorum

    for (let i = 0; i < commentCount; i++) {
        let commentText;
        let rating;

        const rand = Math.random();
        if (rand < 0.6) {
            // %60 pozitif
            commentText = positiveComments[Math.floor(Math.random() * positiveComments.length)];
            rating = Math.floor(Math.random() * 2) + 4; // 4-5 yıldız
        } else if (rand < 0.8) {
            // %20 nötr
            commentText = neutralComments[Math.floor(Math.random() * neutralComments.length)];
            rating = 3; // 3 yıldız
        } else {
            // %20 negatif
            commentText = negativeComments[Math.floor(Math.random() * negativeComments.length)];
            rating = Math.floor(Math.random() * 2) + 1; // 1-2 yıldız
        }

        const daysAgo = Math.floor(Math.random() * 90) + 1;
        const date = new Date();
        date.setDate(date.getDate() - daysAgo);

        comments.push({
            comment: commentText,
            rating: `${rating}/5`,
            author: userNames[Math.floor(Math.random() * userNames.length)],
            date: date.toLocaleDateString("tr-TR"),
        });
    }

    return comments;
}

// Ürün bilgisi oluşturma
function generateProductInfo(searchQuery) {
    const prices = ["₺299,99", "₺1.299,99", "₺599,99", "₺899,99", "₺1.999,99", "₺449,99"];
    const brands = ["Samsung", "Apple", "Xiaomi", "Huawei", "LG", "Sony", "Nike", "Adidas"];

    const randomBrand = brands[Math.floor(Math.random() * brands.length)];
    const randomPrice = prices[Math.floor(Math.random() * prices.length)];

    return {
        title: `${randomBrand} ${searchQuery}`,
        price: randomPrice,
        url: `https://www.trendyol.com/demo-product-${Date.now()}`,
    };
}

// Yorumları çekme endpoint'i (AI destekli gerçek scraping)
app.post("/api/scrape-comments", async (req, res) => {
    const { searchQuery } = req.body;

    if (!searchQuery) {
        return res.status(400).json({ error: "Arama terimi gerekli" });
    }

    try {
        console.log(`Gerçek veri çekiliyor: ${searchQuery}`);

        // Önce gerçek Trendyol verilerini çekmeye çalış
        let result = await scrapeRealTrendyolData(searchQuery);

        if (!result || !result.success || result.comments.length === 0) {
            console.log("Gerçek veri çekilemedi, sahte veri oluşturuluyor...");

            // Gerçek veri çekilemezse sahte veri oluştur
            const comments = generateMockComments(searchQuery);
            const productInfo = generateProductInfo(searchQuery);

            result = {
                success: true,
                productTitle: productInfo.title,
                productPrice: productInfo.price,
                productUrl: productInfo.url,
                comments,
                dataSource: "Mock Data (Real data unavailable)",
            };
        }

        console.log(`${result.comments.length} yorum bulundu - Kaynak: ${result.dataSource}`);

        res.json({
            success: true,
            productTitle: result.productTitle,
            productPrice: result.productPrice,
            productUrl: result.productUrl,
            searchQuery,
            comments: result.comments,
            totalComments: result.comments.length,
            dataSource: result.dataSource,
        });
    } catch (error) {
        console.error("Veri çekme hatası:", error);
        res.status(500).json({
            error: "Veri çekme sırasında hata oluştu",
            details: error.message,
        });
    }
});

app.listen(PORT, () => {
    console.log(`Server http://localhost:${PORT} adresinde çalışıyor`);
    console.log(`AI API: ${OPENAI_API_KEY ? "Aktif" : "Pasif (Mock data kullanılacak)"}`);
});
