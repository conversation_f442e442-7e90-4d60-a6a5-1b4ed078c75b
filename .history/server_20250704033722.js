const express = require("express");
const { chromium } = require("playwright");
const cors = require("cors");
const path = require("path");

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static("public"));

// Ana sayfa
app.get("/", (req, res) => {
    res.sendFile(path.join(__dirname, "public", "index.html"));
});

// Yorumları çekme endpoint'i
app.post("/api/scrape-comments", async (req, res) => {
    const { searchQuery } = req.body;

    if (!searchQuery) {
        return res.status(400).json({ error: "Arama terimi gerekli" });
    }

    let browser;
    try {
        console.log(
            `Arama başlatılıyor: ${searchQuery}`,
            `https://www.trendyol.com/sr?q=${encodeURIComponent(searchQuery)}`,
            `https://www.trendyol.com/sr?q=${encodeURIComponent(
                searchQuery
            )}&qt=${encodeURIComponent(searchQuery)}&st=${encodeURIComponent(searchQuery)}&os=1`
        );

        browser = await chromium.launch({
            headless: true,
            timeout: 30000,
        });

        const page = await browser.newPage();

        // Trendyol arama sayfasına git
        await page.goto(
            `https://www.trendyol.com/sr?q=${encodeURIComponent(
                searchQuery
            )}&qt=${encodeURIComponent(searchQuery)}&st=${encodeURIComponent(searchQuery)}&os=1`,
            {
                waitUntil: "networkidle",
                timeout: 30000,
            }
        );

        // İlk ürünü bul ve tıkla
        await page.waitForSelector('[data-testid="product-card-image"]', { timeout: 30000 });
        const firstProductLink = await page.getAttribute(
            '[data-testid="product-card-image"]',
            "href"
        );

        console.log(`İlk ürünün linki: https://www.trendyol.com${firstProductLink}`);

        if (!firstProductLink) {
            throw new Error("Ürün bulunamadı");
        }

        const productUrl = `https://www.trendyol.com${firstProductLink}`;
        console.log(`Ürün sayfasına gidiliyor: ${productUrl}`);

        await page.goto(productUrl, {
            waitUntil: "networkidle",
            timeout: 30000,
        });

        // Ürün bilgilerini al
        const productTitle = await page.textContent("h1").catch(() => "Ürün adı bulunamadı");
        const productPrice = await page.textContent(".prc-dsc").catch(() => "Fiyat bulunamadı");

        // Yorumlar bölümüne scroll yap
        try {
            await page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight / 2);
            });
            await page.waitForTimeout(2000);
        } catch (error) {
            console.log("Scroll hatası:", error.message);
        }

        // Yorumları bekle ve çek
        let comments = [];
        try {
            await page.waitForSelector('[data-testid="review-card"]', { timeout: 30000 });

            comments = await page.$$eval('[data-testid="review-card"]', (cards) =>
                cards.slice(0, 10).map((card) => {
                    const comment =
                        card.querySelector('[data-testid="review-content"]')?.innerText ||
                        "Yorum metni bulunamadı";
                    const ratingElement = card.querySelector('[data-testid="review-rating"]');
                    let rating = "Puan bulunamadı";

                    if (ratingElement) {
                        // Yıldız sayısını say
                        const stars = ratingElement.querySelectorAll(".star-filled, .star-full");
                        rating = stars.length > 0 ? `${stars.length}/5` : "Puan bulunamadı";
                    }

                    const date =
                        card.querySelector(".review-date")?.innerText || "Tarih bulunamadı";
                    const author = card.querySelector(".review-author")?.innerText || "Kullanıcı";

                    return {
                        comment: comment.trim(),
                        rating,
                        date: date.trim(),
                        author: author.trim(),
                    };
                })
            );
        } catch (error) {
            console.log("Yorum çekme hatası:", error.message);
            comments = [];
        }

        console.log(`${comments.length} yorum bulundu`);

        res.json({
            success: true,
            productTitle,
            productPrice,
            productUrl,
            searchQuery,
            comments,
            totalComments: comments.length,
        });
    } catch (error) {
        console.error("Scraping hatası:", error);
        res.status(500).json({
            error: "Veri çekme sırasında hata oluştu",
            details: error.message,
        });
    } finally {
        if (browser) {
            await browser.close();
        }
    }
});

app.listen(PORT, () => {
    console.log(`Server http://localhost:${PORT} adresinde çalışıyor`);
});
