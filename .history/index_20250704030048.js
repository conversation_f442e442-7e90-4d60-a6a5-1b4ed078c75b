// scrape-trendyol.js
const { chromium } = require("playwright");

(async () => {
    const browser = await chromium.launch({ headless: true });
    const page = await browser.newPage();

    const searchQuery = "iphone 14";
    await page.goto(`https://www.trendyol.com/sr?q=${encodeURIComponent(searchQuery)}`);

    // İlk ürüne tıkla
    await page.waitForSelector('[data-testid="product-card-image"]');
    const firstProductLink = await page.getAttribute('[data-testid="product-card-image"]', "href");
    const productUrl = `https://www.trendyol.com${firstProductLink}`;
    await page.goto(productUrl);

    // Yorumlar yüklensin
    await page.waitForSelector('[data-testid="review-card"]');

    const comments = await page.$$eval('[data-testid="review-card"]', (cards) =>
        cards.map((card) => {
            const comment = card.querySelector('[data-testid="review-content"]')?.innerText;
            const rating = card.querySelector('[data-testid="review-rating"]')?.innerText;
            return { comment, rating };
        })
    );

    console.log(comments);

    await browser.close();
})();
