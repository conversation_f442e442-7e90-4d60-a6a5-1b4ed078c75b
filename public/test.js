const axios = require("axios");
const levenshtein = require("fast-levenshtein");

const searchTrendyol = async (query) => {
    const url = `https://www.trendyol.com/sr?q=${encodeURIComponent(query)}`;
    const { data } = await axios.get(url, {
        headers: { "User-Agent": "Mozilla/5.0" },
    });

    const regex = /<a.*?href="(\/.*?\/.*?)".*?data-id.*?>(.*?)<\/a>/g;
    const products = [];

    let match;
    while ((match = regex.exec(data)) !== null) {
        const link = "https://www.trendyol.com" + match[1];
        const title = match[2]
            .replace(/<[^>]+>/g, "") // HTML etiketlerini temizle
            .replace(/\s+/g, " ")
            .trim();
        products.push({ title, link });
    }

    return products;
};

const scoreProduct = (target, candidate) => {
    const t = target.toLowerCase();
    const c = candidate.toLowerCase();

    const lev = levenshtein.get(t, c);
    const commonWords = t.split(" ").filter((w) => c.includes(w)).length;

    // Basit skor sistemi: daha az fark + daha çok ortak kelime = daha iyi
    return commonWords * 10 - lev;
};

const findBestProduct = async (searchTerm) => {
    const products = await searchTrendyol(searchTerm);

    if (!products.length) return console.log("Ürün bulunamadı.");

    const scored = products.map((p) => ({
        ...p,
        score: scoreProduct(searchTerm, p.title),
    }));

    scored.sort((a, b) => b.score - a.score);

    console.log("📦 En iyi eşleşen ürün:");
    console.log("Ürün:", scored[0].title);
    console.log("Link:", scored[0].link);
};

findBestProduct("iPhone 14 128 GB");
