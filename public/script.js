document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('searchForm');
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    const loading = document.getElementById('loading');
    const error = document.getElementById('error');
    const results = document.getElementById('results');
    const productInfo = document.getElementById('productInfo');
    const commentsList = document.getElementById('commentsList');
    const commentsCount = document.getElementById('commentsCount');

    searchForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const searchQuery = searchInput.value.trim();
        if (!searchQuery) {
            showError('Lütfen bir ürün adı girin');
            return;
        }

        // UI durumunu güncelle
        hideError();
        hideResults();
        showLoading();
        searchBtn.disabled = true;

        try {
            const response = await fetch('/api/scrape-comments', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ searchQuery })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Bir hata oluştu');
            }

            if (data.success) {
                displayResults(data);
            } else {
                throw new Error('Veri çekme başarısız');
            }

        } catch (err) {
            console.error('Hata:', err);
            showError(err.message || 'Bir hata oluştu. Lütfen tekrar deneyin.');
        } finally {
            hideLoading();
            searchBtn.disabled = false;
        }
    });

    function showLoading() {
        loading.style.display = 'block';
    }

    function hideLoading() {
        loading.style.display = 'none';
    }

    function showError(message) {
        error.textContent = message;
        error.style.display = 'block';
    }

    function hideError() {
        error.style.display = 'none';
    }

    function showResults() {
        results.style.display = 'block';
    }

    function hideResults() {
        results.style.display = 'none';
    }

    function displayResults(data) {
        // Ürün bilgilerini göster
        productInfo.innerHTML = `
            <div class="product-title">
                <i class="fas fa-box"></i> ${data.productTitle}
            </div>
            <div class="product-price">
                <i class="fas fa-tag"></i> ${data.productPrice}
            </div>
            <a href="${data.productUrl}" target="_blank" class="product-url">
                <i class="fas fa-external-link-alt"></i> Ürünü Trendyol'da Görüntüle
            </a>
        `;

        // Yorum sayısını güncelle
        commentsCount.textContent = `${data.totalComments} Yorum`;

        // Yorumları listele
        if (data.comments && data.comments.length > 0) {
            commentsList.innerHTML = data.comments.map(comment => `
                <div class="comment-card">
                    <div class="comment-header">
                        <span class="comment-author">
                            <i class="fas fa-user"></i> ${comment.author}
                        </span>
                        <span class="comment-rating">
                            <i class="fas fa-star"></i> ${comment.rating}
                        </span>
                    </div>
                    <div class="comment-text">${comment.comment}</div>
                    <div class="comment-date">
                        <i class="fas fa-calendar"></i> ${comment.date}
                    </div>
                </div>
            `).join('');
        } else {
            commentsList.innerHTML = `
                <div class="no-comments">
                    <i class="fas fa-comment-slash" style="font-size: 3rem; color: #bdc3c7; margin-bottom: 20px;"></i>
                    <h3>Henüz yorum bulunamadı</h3>
                    <p>Bu ürün için henüz kullanıcı yorumu bulunmuyor.</p>
                </div>
            `;
        }

        showResults();
        
        // Sonuçlara scroll yap
        results.scrollIntoView({ behavior: 'smooth' });
    }

    // Enter tuşu ile arama
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchForm.dispatchEvent(new Event('submit'));
        }
    });

    // Örnek arama terimleri için placeholder animasyonu
    const examples = [
        'iPhone 14',
        'Samsung TV',
        'Nike Ayakkabı',
        'Laptop',
        'Kulaklık',
        'Telefon Kılıfı'
    ];
    
    let currentExample = 0;
    setInterval(() => {
        if (searchInput.value === '') {
            searchInput.placeholder = `Ürün adını girin (örn: ${examples[currentExample]}...)`;
            currentExample = (currentExample + 1) % examples.length;
        }
    }, 3000);
});
